import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Platform,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import DateTimePicker from '@react-native-community/datetimepicker';
import {
  X,
  Calendar as CalendarIcon,
  Clock,
  CheckCircle,
  Star,
  Bell,
  Trash2,
} from 'lucide-react-native';
import SubjectPicker from '@/components/SubjectPicker';
import { Task } from '@/types/app';

interface TaskModalProps {
  visible: boolean;
  editingTask: Task | null;
  title: string;
  setTitle: (title: string) => void;
  description: string;
  setDescription: (description: string) => void;
  dueDate: Date | undefined;
  setDueDate: (date: Date | undefined) => void;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  setPriority: (priority: 'low' | 'medium' | 'high' | 'urgent') => void;
  reminderEnabled: boolean;
  setReminderEnabled: (enabled: boolean) => void;
  reminderTime: Date | undefined;
  setReminderTime: (time: Date | undefined) => void;
  periodicReminders: boolean;
  setPeriodicReminders: (enabled: boolean) => void;
  reminderInterval: 'daily' | 'weekly' | 'monthly';
  setReminderInterval: (interval: 'daily' | 'weekly' | 'monthly') => void;
  selectedSubject: any;
  setSelectedSubject: (subject: any) => void;
  isMilestone: boolean;
  setIsMilestone: (milestone: boolean) => void;
  onClose: () => void;
  onSave: () => void;
  onDelete?: () => void;
  getPriorityColor: (priority: 'low' | 'medium' | 'high' | 'urgent') => string;
  getPriorityIcon: (priority: 'low' | 'medium' | 'high' | 'urgent') => React.ReactNode;
  modalScale: Animated.SharedValue<number>;
}

const TaskModal: React.FC<TaskModalProps> = ({
  visible,
  editingTask,
  title,
  setTitle,
  description,
  setDescription,
  dueDate,
  setDueDate,
  priority,
  setPriority,
  reminderEnabled,
  setReminderEnabled,
  reminderTime,
  setReminderTime,
  periodicReminders,
  setPeriodicReminders,
  reminderInterval,
  setReminderInterval,
  selectedSubject,
  setSelectedSubject,
  isMilestone,
  setIsMilestone,
  onClose,
  onSave,
  onDelete,
  getPriorityColor,
  getPriorityIcon,
  modalScale,
}) => {
  // Date/Time picker states
  const [showDueDatePicker, setShowDueDatePicker] = useState(false);
  const [showReminderDatePicker, setShowReminderDatePicker] = useState(false);
  const [showReminderTimePicker, setShowReminderTimePicker] = useState(false);

  const modalAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: modalScale.value }],
  }));

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.modalOverlay}>
        <Animated.View style={[styles.modalContent, modalAnimatedStyle]}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {editingTask ? 'Edit Task' : 'Create New Task'}
            </Text>
            <TouchableOpacity onPress={onClose}>
              <X size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
            {/* Task Title */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Task Title *</Text>
              <TextInput
                style={styles.textInput}
                value={title}
                onChangeText={setTitle}
                placeholder="Enter task title"
                placeholderTextColor="#9CA3AF"
              />
            </View>

            {/* Description */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Description</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={description}
                onChangeText={setDescription}
                placeholder="Enter task description"
                placeholderTextColor="#9CA3AF"
                multiline
                numberOfLines={3}
              />
            </View>

            {/* Due Date */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Due Date</Text>
              <TouchableOpacity
                style={styles.datePickerButton}
                onPress={() => setShowDueDatePicker(true)}
              >
                <CalendarIcon size={20} color="#6B7280" />
                <Text style={styles.datePickerText}>
                  {dueDate ? dueDate.toLocaleDateString() : 'Select due date'}
                </Text>
              </TouchableOpacity>
              {showDueDatePicker && (
                <DateTimePicker
                  value={dueDate || new Date()}
                  mode="date"
                  display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                  onChange={(event, selectedDate) => {
                    setShowDueDatePicker(false);
                    if (selectedDate) {
                      setDueDate(selectedDate);
                    }
                  }}
                />
              )}
            </View>

            {/* Priority */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Priority</Text>
              <View style={styles.prioritySelector}>
                {(['low', 'medium', 'high', 'urgent'] as const).map((p) => (
                  <TouchableOpacity
                    key={p}
                    style={[
                      styles.priorityButton,
                      priority === p && styles.priorityButtonActive,
                      { borderColor: getPriorityColor(p) },
                    ]}
                    onPress={() => setPriority(p)}
                  >
                    {getPriorityIcon(p)}
                    <Text
                      style={[
                        styles.priorityButtonText,
                        priority === p && { color: getPriorityColor(p) },
                      ]}
                    >
                      {p.charAt(0).toUpperCase() + p.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Milestone Toggle */}
            <View style={styles.inputGroup}>
              <TouchableOpacity
                style={styles.checkboxRow}
                onPress={() => setIsMilestone(!isMilestone)}
              >
                <View style={[styles.checkbox, isMilestone && styles.checkboxActive]}>
                  {isMilestone && <CheckCircle size={16} color="#FFFFFF" />}
                </View>
                <Text style={styles.checkboxLabel}>Mark as milestone</Text>
                <Star size={16} color="#F59E0B" />
              </TouchableOpacity>
            </View>

            {/* Reminder Settings */}
            <View style={styles.inputGroup}>
              <TouchableOpacity
                style={styles.checkboxRow}
                onPress={() => setReminderEnabled(!reminderEnabled)}
              >
                <View style={[styles.checkbox, reminderEnabled && styles.checkboxActive]}>
                  {reminderEnabled && <CheckCircle size={16} color="#FFFFFF" />}
                </View>
                <Text style={styles.checkboxLabel}>Enable reminders</Text>
                <Bell size={16} color="#3B82F6" />
              </TouchableOpacity>

              {reminderEnabled && (
                <View style={styles.reminderTimeContainer}>
                  <Text style={styles.reminderTimeLabel}>Reminder Date & Time</Text>

                  {/* Reminder Date */}
                  <TouchableOpacity
                    style={styles.textInput}
                    onPress={() => setShowReminderDatePicker(true)}
                  >
                    <View style={styles.dateTimePickerRow}>
                      <CalendarIcon size={16} color="#6B7280" />
                      <Text style={styles.dateTimePickerText}>
                        {reminderTime ? reminderTime.toLocaleDateString() : 'Select date'}
                      </Text>
                    </View>
                  </TouchableOpacity>

                  {/* Reminder Time */}
                  <TouchableOpacity
                    style={styles.textInput}
                    onPress={() => setShowReminderTimePicker(true)}
                  >
                    <View style={styles.dateTimePickerRow}>
                      <Clock size={16} color="#6B7280" />
                      <Text style={styles.dateTimePickerText}>
                        {reminderTime ? reminderTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : 'Select time'}
                      </Text>
                    </View>
                  </TouchableOpacity>

                  {/* Periodic Reminders */}
                  <TouchableOpacity
                    style={styles.checkboxRow}
                    onPress={() => setPeriodicReminders(!periodicReminders)}
                  >
                    <View style={[styles.checkbox, periodicReminders && styles.checkboxActive]}>
                      {periodicReminders && <CheckCircle size={16} color="#FFFFFF" />}
                    </View>
                    <Text style={styles.checkboxLabel}>Repeat reminders</Text>
                  </TouchableOpacity>

                  {periodicReminders && (
                    <View style={styles.reminderIntervalContainer}>
                      <Text style={styles.reminderIntervalLabel}>Repeat every:</Text>
                      <View style={styles.intervalSelector}>
                        {(['daily', 'weekly', 'monthly'] as const).map((interval) => (
                          <TouchableOpacity
                            key={interval}
                            style={[
                              styles.intervalButton,
                              reminderInterval === interval && styles.intervalButtonActive
                            ]}
                            onPress={() => setReminderInterval(interval)}
                          >
                            <Text style={[
                              styles.intervalButtonText,
                              reminderInterval === interval && styles.intervalButtonActiveText
                            ]}>
                              {interval.charAt(0).toUpperCase() + interval.slice(1)}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    </View>
                  )}

                  {/* Date/Time Pickers */}
                  {showReminderDatePicker && (
                    <DateTimePicker
                      value={reminderTime || new Date()}
                      mode="date"
                      display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                      onChange={(_, selectedDate) => {
                        setShowReminderDatePicker(false);
                        if (selectedDate) {
                          const newDateTime = reminderTime ? new Date(reminderTime) : new Date();
                          newDateTime.setFullYear(selectedDate.getFullYear());
                          newDateTime.setMonth(selectedDate.getMonth());
                          newDateTime.setDate(selectedDate.getDate());
                          setReminderTime(newDateTime);
                        }
                      }}
                    />
                  )}

                  {showReminderTimePicker && (
                    <DateTimePicker
                      value={reminderTime || new Date()}
                      mode="time"
                      display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                      onChange={(_, selectedTime) => {
                        setShowReminderTimePicker(false);
                        if (selectedTime) {
                          const newDateTime = reminderTime ? new Date(reminderTime) : new Date();
                          newDateTime.setHours(selectedTime.getHours());
                          newDateTime.setMinutes(selectedTime.getMinutes());
                          setReminderTime(newDateTime);
                        }
                      }}
                    />
                  )}

                  <Text style={styles.reminderHint}>
                    Set when you want to be reminded about this task
                  </Text>
                </View>
              )}
            </View>

            {/* Subject */}
            <View style={styles.inputGroup}>
              <SubjectPicker
                selectedSubject={selectedSubject}
                onSelectSubject={setSelectedSubject}
              />
            </View>
          </ScrollView>
          
          <View style={styles.modalActions}>
            {editingTask && onDelete && (
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={() => {
                  onClose();
                  onDelete();
                }}
              >
                <Trash2 size={16} color="#EF4444" />
                <Text style={styles.deleteText}>Delete</Text>
              </TouchableOpacity>
            )}

            <View style={styles.actionButtons}>
              <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
                <Text style={styles.cancelText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.saveButton} onPress={onSave}>
                <LinearGradient
                  colors={['#6366F1', '#8B5CF6']}
                  style={styles.saveGradient}
                >
                  <Text style={styles.saveText}>
                    {editingTask ? 'Update' : 'Create'}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    width: '100%',
    maxHeight: '90%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
  },
  modalBody: {
    padding: 20,
    maxHeight: 400,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1F2937',
    backgroundColor: '#FFFFFF',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    gap: 12,
  },
  datePickerText: {
    fontSize: 16,
    color: '#1F2937',
  },
  prioritySelector: {
    flexDirection: 'row',
    gap: 8,
  },
  priorityButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 2,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    gap: 6,
  },
  priorityButtonActive: {
    backgroundColor: '#F9FAFB',
  },
  priorityButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  checkboxActive: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  checkboxLabel: {
    fontSize: 16,
    color: '#374151',
    flex: 1,
  },
  reminderTimeContainer: {
    marginTop: 16,
    gap: 12,
  },
  reminderTimeLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  dateTimePickerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  dateTimePickerText: {
    fontSize: 16,
    color: '#1F2937',
  },
  reminderIntervalContainer: {
    gap: 8,
  },
  reminderIntervalLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  intervalSelector: {
    flexDirection: 'row',
    gap: 8,
  },
  intervalButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
  },
  intervalButtonActive: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  intervalButtonText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  intervalButtonActiveText: {
    color: '#FFFFFF',
  },
  reminderHint: {
    fontSize: 12,
    color: '#9CA3AF',
    fontStyle: 'italic',
    marginTop: 8,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  deleteText: {
    fontSize: 16,
    color: '#EF4444',
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: '#F3F4F6',
  },
  cancelText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '500',
  },
  saveButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  saveGradient: {
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  saveText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});

export default TaskModal;

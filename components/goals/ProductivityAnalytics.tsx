import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import {
  Activity,
  Clock,
  TrendingUp,
} from 'lucide-react-native';
import { Task } from '@/types/app';

interface ProductivityAnalyticsProps {
  tasks: Task[];
}

const ProductivityAnalytics: React.FC<ProductivityAnalyticsProps> = ({ tasks }) => {
  // Calculate productivity metrics
  const completedTasks = tasks.filter(task => task.status === 'completed');
  const totalTasks = tasks.length;
  const completionRate = totalTasks > 0 ? (completedTasks.length / totalTasks) * 100 : 0;

  // Calculate average completion time
  const tasksWithDuration = completedTasks.filter(task => task.actual_duration);
  const avgCompletionTime = tasksWithDuration.length > 0
    ? tasksWithDuration.reduce((sum, task) => sum + (task.actual_duration || 0), 0) / tasksWithDuration.length
    : 0;

  // Calculate productivity by priority
  const priorityStats = ['urgent', 'high', 'medium', 'low'].map(priority => {
    const priorityTasks = tasks.filter(task => task.priority === priority);
    const priorityCompleted = priorityTasks.filter(task => task.status === 'completed');
    const rate = priorityTasks.length > 0 ? (priorityCompleted.length / priorityTasks.length) * 100 : 0;

    return {
      priority,
      total: priorityTasks.length,
      completed: priorityCompleted.length,
      rate,
      color: priority === 'urgent' ? '#EF4444' :
             priority === 'high' ? '#F59E0B' :
             priority === 'medium' ? '#3B82F6' : '#10B981'
    };
  });

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${Math.round(minutes)}m`;
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}h ${mins}m`;
  };

  return (
    <View style={styles.productivityAnalytics}>
      <Text style={styles.analyticsTitle}>Productivity Analytics</Text>

      {/* Overall Stats */}
      <View style={styles.analyticsOverview}>
        <View style={styles.analyticsCard}>
          <Activity size={20} color="#3B82F6" />
          <Text style={styles.analyticsValue}>{Math.round(completionRate)}%</Text>
          <Text style={styles.analyticsLabel}>Completion Rate</Text>
        </View>
        <View style={styles.analyticsCard}>
          <Clock size={20} color="#10B981" />
          <Text style={styles.analyticsValue}>{formatDuration(avgCompletionTime)}</Text>
          <Text style={styles.analyticsLabel}>Avg. Time</Text>
        </View>
        <View style={styles.analyticsCard}>
          <TrendingUp size={20} color="#F59E0B" />
          <Text style={styles.analyticsValue}>{completedTasks.length}</Text>
          <Text style={styles.analyticsLabel}>Completed</Text>
        </View>
      </View>

      {/* Priority Breakdown */}
      <View style={styles.priorityBreakdown}>
        <Text style={styles.breakdownTitle}>Completion by Priority</Text>
        {priorityStats.map((stat) => (
          <View key={stat.priority} style={styles.priorityRow}>
            <View style={styles.priorityInfo}>
              <View style={[styles.priorityDot, { backgroundColor: stat.color }]} />
              <Text style={styles.priorityName}>
                {stat.priority.charAt(0).toUpperCase() + stat.priority.slice(1)}
              </Text>
            </View>
            <View style={styles.priorityStats}>
              <Text style={styles.priorityCount}>{stat.completed}/{stat.total}</Text>
              <Text style={[styles.priorityRate, { color: stat.color }]}>
                {Math.round(stat.rate)}%
              </Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  productivityAnalytics: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  analyticsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  analyticsOverview: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 20,
  },
  analyticsCard: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    gap: 8,
  },
  analyticsValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
  },
  analyticsLabel: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
  },
  priorityBreakdown: {
    gap: 12,
  },
  breakdownTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  priorityRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  priorityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  priorityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  priorityName: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
  priorityStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  priorityCount: {
    fontSize: 14,
    color: '#6B7280',
  },
  priorityRate: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default ProductivityAnalytics;

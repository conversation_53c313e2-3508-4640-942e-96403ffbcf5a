import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
} from 'react-native';
import Animated from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Plus,
  Filter,
  BarChart3,
  Search,
} from 'lucide-react-native';
import Isotope<PERSON>ogo from '@/components/IsotopeLogo';
import { Task } from '@/types/app';

interface GoalsHeaderProps {
  showStats: boolean;
  setShowStats: (show: boolean) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  selectedView: 'all' | 'todo' | 'in_progress' | 'completed';
  setSelectedView: (view: 'all' | 'todo' | 'in_progress' | 'completed') => void;
  onAddTask: () => void;
  onOpenFilter: () => void;
  tasks: Task[];
}

const GoalsHeader: React.FC<GoalsHeaderProps> = ({
  showStats,
  setShowStats,
  searchQuery,
  setSearchQuery,
  selectedView,
  setSelectedView,
  onAddTask,
  onOpenFilter,
  tasks,
}) => {
  const todoTasks = tasks.filter(task => task.status === 'todo');
  const inProgressTasks = tasks.filter(task => task.status === 'in_progress');
  const completedTasks = tasks.filter(task => task.status === 'completed');
  const overdueTasks = tasks.filter(task => 
    task.due_date && 
    task.due_date < new Date() && 
    task.due_date.toDateString() !== new Date().toDateString()
  );

  return (
    <LinearGradient
      colors={['#F8FAFC', '#EEF2FF', '#F8FAFC']}
      style={styles.headerGradient}
    >
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <IsotopeLogo size="medium" />
          <Text style={styles.subtitle}>Task Management</Text>
        </View>
        <View style={styles.headerRight}>
          <TouchableOpacity style={styles.statsButton} onPress={() => setShowStats(!showStats)}>
            <BarChart3 size={20} color="#6366F1" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.filterButton} onPress={onOpenFilter}>
            <Filter size={20} color="#6366F1" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.addButton} onPress={onAddTask}>
            <Plus size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Search size={20} color="#6B7280" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search tasks..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9CA3AF"
          />
        </View>
      </View>

      {/* Quick Stats */}
      {showStats && (
        <Animated.View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{todoTasks.length}</Text>
            <Text style={styles.statLabel}>To Do</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{inProgressTasks.length}</Text>
            <Text style={styles.statLabel}>In Progress</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{completedTasks.length}</Text>
            <Text style={styles.statLabel}>Completed</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={[styles.statNumber, { color: '#EF4444' }]}>{overdueTasks.length}</Text>
            <Text style={styles.statLabel}>Overdue</Text>
          </View>
        </Animated.View>
      )}

      {/* Filter Tabs */}
      <View style={styles.filterTabs}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tabsContainer}>
          {['all', 'todo', 'in_progress', 'completed'].map((view) => (
            <TouchableOpacity
              key={view}
              style={[
                styles.filterTab,
                selectedView === view && styles.activeFilterTab
              ]}
              onPress={() => setSelectedView(view as any)}
            >
              <Text style={[
                styles.filterTabText,
                selectedView === view && styles.activeFilterTabText
              ]}>
                {view === 'all' ? 'All' :
                 view === 'todo' ? 'To Do' :
                 view === 'in_progress' ? 'In Progress' : 'Completed'}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statsButton: {
    backgroundColor: '#FFFFFF',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  filterButton: {
    backgroundColor: '#FFFFFF',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  addButton: {
    width: 44,
    height: 44,
    backgroundColor: '#6366F1',
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#1F2937',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingTop: 16,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  statLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  filterTabs: {
    paddingTop: 16,
  },
  tabsContainer: {
    paddingHorizontal: 20,
  },
  filterTab: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  activeFilterTab: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  filterTabText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  activeFilterTabText: {
    color: '#FFFFFF',
  },
});

export default GoalsHeader;

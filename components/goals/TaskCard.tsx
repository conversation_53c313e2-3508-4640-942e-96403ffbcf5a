import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import Animated from 'react-native-reanimated';
import {
  Star,
  CheckCircle,
  Award,
  Calendar as CalendarIcon,
  MoreVertical,
} from 'lucide-react-native';
import { Task } from '@/types/app';

interface TaskCardProps {
  task: Task;
  onToggleComplete: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onUpdateProgress: (progress: number) => void;
  getPriorityColor: (priority: Task['priority']) => string;
  getPriorityIcon: (priority: Task['priority']) => React.ReactNode;
  getStatusIcon: (status: Task['status']) => React.ReactNode;
  formatDate: (date: Date) => string;
  isOverdue: (date: Date) => boolean;
  getSubjectById: (id: string) => any;
}

const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onToggleComplete,
  onEdit,
  onUpdateProgress,
  getPriorityColor,
  getPriorityIcon,
  getStatusIcon,
  formatDate,
  isOverdue,
  getSubjectById,
}) => {
  const subject = task.subject_id ? getSubjectById(task.subject_id) : null;
  const isTaskOverdue = task.due_date && isOverdue(task.due_date);

  return (
    <View style={[
      styles.taskCard,
      isTaskOverdue && styles.overdueCard,
      task.status === 'completed' && styles.completedCard
    ]}>
      {/* Task Header */}
      <View style={styles.taskHeader}>
        <TouchableOpacity onPress={onToggleComplete} style={styles.statusButton}>
          {getStatusIcon(task.status)}
        </TouchableOpacity>

        <View style={styles.taskInfo}>
          <View style={styles.taskTitleRow}>
            <Text style={[
              styles.taskTitle,
              task.status === 'completed' && styles.completedTitle
            ]}>
              {task.title}
            </Text>
            {task.is_milestone && (
              <View style={styles.milestoneTag}>
                <Star size={12} color="#F59E0B" />
              </View>
            )}
          </View>

          {task.description && (
            <Text style={[
              styles.taskDescription,
              task.status === 'completed' && styles.completedDescription
            ]}>
              {task.description}
            </Text>
          )}

          {/* Tags */}
          {task.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {task.tags.slice(0, 3).map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                </View>
              ))}
              {task.tags.length > 3 && (
                <Text style={styles.moreTagsText}>+{task.tags.length - 3}</Text>
              )}
            </View>
          )}

          {/* Subject */}
          {subject && (
            <View style={styles.subjectTag}>
              <View style={[styles.subjectDot, { backgroundColor: subject.color }]} />
              <Text style={styles.subjectName}>{subject.name}</Text>
            </View>
          )}
        </View>

        <TouchableOpacity onPress={onEdit} style={styles.editButton}>
          <MoreVertical size={16} color="#6B7280" />
        </TouchableOpacity>
      </View>

      {/* Enhanced Progress Bar */}
      {task.progress_percentage > 0 && (
        <View style={styles.progressContainer}>
          <View style={styles.progressHeader}>
            <Text style={styles.progressLabel}>Progress</Text>
            <View style={styles.progressBadge}>
              <Text style={styles.progressText}>{task.progress_percentage}%</Text>
            </View>
          </View>
          <View style={styles.progressBar}>
            <Animated.View
              style={[
                styles.progressFill,
                {
                  width: `${task.progress_percentage}%`,
                  backgroundColor: getPriorityColor(task.priority)
                }
              ]}
            />
            {task.progress_percentage >= 100 && (
              <View style={styles.completionIndicator}>
                <CheckCircle size={12} color="#10B981" />
              </View>
            )}
          </View>
          {task.is_milestone && (
            <View style={styles.milestoneIndicator}>
              <Award size={14} color="#F59E0B" />
              <Text style={styles.milestoneText}>Milestone</Text>
            </View>
          )}

          {/* Interactive Progress Slider */}
          {task.status !== 'completed' && task.status !== 'cancelled' && (
            <View style={styles.progressSliderContainer}>
              <Text style={styles.sliderLabel}>Update Progress</Text>
              <View style={styles.progressSlider}>
                <TouchableOpacity
                  style={[styles.progressSliderTrack, { width: '100%' }]}
                  onPress={(event) => {
                    const { locationX } = event.nativeEvent;
                    const sliderWidth = 200; // Approximate slider width
                    const newProgress = Math.round((locationX / sliderWidth) * 100);
                    const clampedProgress = Math.max(0, Math.min(100, newProgress));
                    onUpdateProgress(clampedProgress);
                  }}
                >
                  <View style={[styles.progressSliderFill, { width: `${task.progress_percentage}%` }]} />
                  <View
                    style={[
                      styles.progressSliderThumb,
                      { left: `${task.progress_percentage}%` }
                    ]}
                  />
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      )}

      {/* Task Meta */}
      <View style={styles.taskMeta}>
        <View style={styles.taskMetaLeft}>
          {/* Priority */}
          <View style={styles.priorityTag}>
            {getPriorityIcon(task.priority)}
            <Text style={[styles.priorityText, { color: getPriorityColor(task.priority) }]}>
              {task.priority}
            </Text>
          </View>
        </View>

        <View style={styles.taskMetaRight}>
          {/* Due Date */}
          {task.due_date && (
            <View style={styles.dueDateContainer}>
              <CalendarIcon size={14} color={isTaskOverdue ? '#EF4444' : '#6B7280'} />
              <Text style={[
                styles.dueDateText,
                isTaskOverdue && styles.overdueDateText
              ]}>
                {formatDate(task.due_date)}
              </Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  taskCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  overdueCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#EF4444',
  },
  completedCard: {
    opacity: 0.7,
  },
  taskHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  statusButton: {
    marginRight: 12,
    marginTop: 2,
  },
  taskInfo: {
    flex: 1,
  },
  taskTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    flex: 1,
  },
  completedTitle: {
    textDecorationLine: 'line-through',
    color: '#6B7280',
  },
  milestoneTag: {
    marginLeft: 8,
  },
  taskDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
    lineHeight: 20,
  },
  completedDescription: {
    color: '#9CA3AF',
  },
  tagsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tag: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 6,
  },
  tagText: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  moreTagsText: {
    fontSize: 12,
    color: '#9CA3AF',
    fontStyle: 'italic',
  },
  subjectTag: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  subjectDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  subjectName: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  editButton: {
    padding: 4,
  },
  progressContainer: {
    marginBottom: 12,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  progressBadge: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  progressText: {
    fontSize: 12,
    color: '#374151',
    fontWeight: '600',
  },
  progressBar: {
    height: 6,
    backgroundColor: '#F3F4F6',
    borderRadius: 3,
    overflow: 'hidden',
    position: 'relative',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  completionIndicator: {
    position: 'absolute',
    right: 4,
    top: -3,
  },
  milestoneIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    gap: 4,
  },
  milestoneText: {
    fontSize: 12,
    color: '#F59E0B',
    fontWeight: '500',
  },
  progressSliderContainer: {
    marginTop: 12,
  },
  sliderLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 8,
  },
  progressSlider: {
    alignItems: 'center',
  },
  progressSliderTrack: {
    height: 20,
    backgroundColor: '#F3F4F6',
    borderRadius: 10,
    justifyContent: 'center',
    position: 'relative',
  },
  progressSliderFill: {
    height: 6,
    backgroundColor: '#6366F1',
    borderRadius: 3,
    position: 'absolute',
    left: 0,
  },
  progressSliderThumb: {
    width: 16,
    height: 16,
    backgroundColor: '#6366F1',
    borderRadius: 8,
    position: 'absolute',
    marginLeft: -8,
  },
  taskMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  taskMetaLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priorityTag: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  taskMetaRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dueDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  dueDateText: {
    fontSize: 12,
    color: '#6B7280',
  },
  overdueDateText: {
    color: '#EF4444',
    fontWeight: '600',
  },
});

export default TaskCard;

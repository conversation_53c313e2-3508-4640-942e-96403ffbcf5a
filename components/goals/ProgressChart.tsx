import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
} from 'react-native';
import Animated from 'react-native-reanimated';
import { Task } from '@/types/app';

const { width: screenWidth } = Dimensions.get('window');

interface ProgressChartProps {
  tasks: Task[];
}

const ProgressChart: React.FC<ProgressChartProps> = ({ tasks }) => {
  const chartWidth = screenWidth - 32;
  const chartHeight = 200;

  // Calculate priority-based progress
  const priorities = ['urgent', 'high', 'medium', 'low'] as const;
  const priorityColors = {
    urgent: '#DC2626',
    high: '#EA580C',
    medium: '#D97706',
    low: '#65A30D'
  };

  const priorityProgress = priorities.map(priority => {
    const priorityTasks = tasks.filter(task => task.priority === priority);
    const completedTasks = priorityTasks.filter(task => task.status === 'completed');
    const progress = priorityTasks.length > 0 ? (completedTasks.length / priorityTasks.length) * 100 : 0;

    return {
      priority,
      color: priorityColors[priority],
      progress,
      total: priorityTasks.length,
      completed: completedTasks.length,
    };
  });

  const maxProgress = Math.max(...priorityProgress.map(pp => pp.progress), 1);

  return (
    <View style={styles.progressChart}>
      <Text style={styles.chartTitle}>Progress by Priority</Text>
      <View style={styles.chartContainer}>
        {priorityProgress.map((item) => (
          <View key={item.priority} style={styles.chartBar}>
            <View style={styles.barContainer}>
              <View style={[styles.barBackground, { width: chartWidth * 0.7 }]}>
                <Animated.View
                  style={[
                    styles.barFill,
                    {
                      backgroundColor: item.color,
                      width: `${item.progress}%`,
                    }
                  ]}
                />
              </View>
              <Text style={styles.progressPercentage}>{Math.round(item.progress)}%</Text>
            </View>
            <View style={styles.barInfo}>
              <Text style={styles.categoryName}>{item.priority.charAt(0).toUpperCase() + item.priority.slice(1)}</Text>
              <Text style={styles.taskCount}>{item.completed}/{item.total} tasks</Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  progressChart: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  chartContainer: {
    gap: 12,
  },
  chartBar: {
    gap: 8,
  },
  barContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  barBackground: {
    height: 8,
    backgroundColor: '#F3F4F6',
    borderRadius: 4,
    overflow: 'hidden',
  },
  barFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressPercentage: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7280',
    minWidth: 35,
    textAlign: 'right',
  },
  barInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  taskCount: {
    fontSize: 12,
    color: '#6B7280',
  },
});

export default ProgressChart;

import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import { Award } from 'lucide-react-native';
import { Task } from '@/types/app';

interface MilestoneProgressProps {
  tasks: Task[];
}

const MilestoneProgress: React.FC<MilestoneProgressProps> = ({ tasks }) => {
  const milestones = tasks.filter(task => task.is_milestone);
  const completedMilestones = milestones.filter(task => task.status === 'completed');
  const progressPercentage = milestones.length > 0 ? (completedMilestones.length / milestones.length) * 100 : 0;

  const animatedProgress = useSharedValue(0);

  useEffect(() => {
    animatedProgress.value = withTiming(progressPercentage, { duration: 1000 });
  }, [progressPercentage]);

  const animatedStyle = useAnimatedStyle(() => ({
    width: `${animatedProgress.value}%`,
  }));

  return (
    <View style={styles.milestoneProgress}>
      <View style={styles.milestoneHeader}>
        <Award size={20} color="#F59E0B" />
        <Text style={styles.milestoneTitle}>Milestone Progress</Text>
      </View>
      <View style={styles.milestoneBar}>
        <Animated.View style={[styles.milestoneBarFill, animatedStyle]} />
      </View>
      <View style={styles.milestoneStats}>
        <Text style={styles.milestoneStatsText}>
          {completedMilestones.length} of {milestones.length} milestones completed
        </Text>
        <Text style={styles.milestonePercentage}>{Math.round(progressPercentage)}%</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  milestoneProgress: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  milestoneHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  milestoneTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  milestoneBar: {
    height: 12,
    backgroundColor: '#FEF3C7',
    borderRadius: 6,
    overflow: 'hidden',
    marginBottom: 12,
  },
  milestoneBarFill: {
    height: '100%',
    backgroundColor: '#F59E0B',
    borderRadius: 6,
  },
  milestoneStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  milestoneStatsText: {
    fontSize: 14,
    color: '#6B7280',
  },
  milestonePercentage: {
    fontSize: 16,
    fontWeight: '600',
    color: '#F59E0B',
  },
});

export default MilestoneProgress;

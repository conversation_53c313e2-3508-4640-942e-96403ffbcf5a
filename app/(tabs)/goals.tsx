import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
  FlatList,
  Dimensions,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import DateTimePicker from '@react-native-community/datetimepicker';
import {
  Plus,
  Target,
  Calendar,
  Flag,
  CircleCheck as CheckCircle2,
  Circle,
  CreditCard as Edit3,
  Trash2,
  X,
  CircleAlert as AlertCircle,
  Search,
  Filter,
  Clock,
  TrendingUp,
  BookOpen,
  Star,
  CheckCircle,
  PlayCircle,
  BarChart3,
  Settings,
  Tag,
  Calendar as CalendarIcon,
  ChevronDown,
  ChevronRight,
  MoreVertical,
  PieChart,
  Activity,
  Award,
  Flame,
  ArrowUp,
  ArrowDown,
  Zap,
  AlertTriangle,
  Bell,
} from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import IsotopeLogo from '@/components/IsotopeLogo';
import SubjectPicker from '@/components/SubjectPicker';
import { useTasks } from '@/hooks/useTasks';
import { useTaskReminders } from '@/hooks/useTaskReminders';
import { useSubjects } from '@/hooks/useSubjects';
import { Task, TaskCategory, TaskFilters, TaskStats } from '@/types/app';

const { width: screenWidth } = Dimensions.get('window');

// Enhanced Progress Chart Component
interface ProgressChartProps {
  tasks: Task[];
}

const ProgressChart: React.FC<ProgressChartProps> = ({ tasks }) => {
  const chartWidth = screenWidth - 32;
  const chartHeight = 200;

  // Calculate priority-based progress
  const priorities = ['urgent', 'high', 'medium', 'low'] as const;
  const priorityColors = {
    urgent: '#DC2626',
    high: '#EA580C',
    medium: '#D97706',
    low: '#65A30D'
  };

  const priorityProgress = priorities.map(priority => {
    const priorityTasks = tasks.filter(task => task.priority === priority);
    const completedTasks = priorityTasks.filter(task => task.status === 'completed');
    const progress = priorityTasks.length > 0 ? (completedTasks.length / priorityTasks.length) * 100 : 0;

    return {
      priority,
      color: priorityColors[priority],
      progress,
      total: priorityTasks.length,
      completed: completedTasks.length,
    };
  });

  const maxProgress = Math.max(...priorityProgress.map(pp => pp.progress), 1);

  return (
    <View style={styles.progressChart}>
      <Text style={styles.chartTitle}>Progress by Priority</Text>
      <View style={styles.chartContainer}>
        {priorityProgress.map((item) => (
          <View key={item.priority} style={styles.chartBar}>
            <View style={styles.barContainer}>
              <View style={[styles.barBackground, { width: chartWidth * 0.7 }]}>
                <Animated.View
                  style={[
                    styles.barFill,
                    {
                      backgroundColor: item.color,
                      width: `${item.progress}%`,
                    }
                  ]}
                />
              </View>
              <Text style={styles.progressPercentage}>{Math.round(item.progress)}%</Text>
            </View>
            <View style={styles.barInfo}>
              <Text style={styles.categoryName}>{item.priority.charAt(0).toUpperCase() + item.priority.slice(1)}</Text>
              <Text style={styles.taskCount}>{item.completed}/{item.total} tasks</Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

// Milestone Progress Component
interface MilestoneProgressProps {
  tasks: Task[];
}

const MilestoneProgress: React.FC<MilestoneProgressProps> = ({ tasks }) => {
  const milestones = tasks.filter(task => task.is_milestone);
  const completedMilestones = milestones.filter(task => task.status === 'completed');
  const progressPercentage = milestones.length > 0 ? (completedMilestones.length / milestones.length) * 100 : 0;

  const animatedProgress = useSharedValue(0);

  useEffect(() => {
    animatedProgress.value = withTiming(progressPercentage, { duration: 1000 });
  }, [progressPercentage]);

  const animatedStyle = useAnimatedStyle(() => ({
    width: `${animatedProgress.value}%`,
  }));

  return (
    <View style={styles.milestoneProgress}>
      <View style={styles.milestoneHeader}>
        <Award size={20} color="#F59E0B" />
        <Text style={styles.milestoneTitle}>Milestone Progress</Text>
      </View>
      <View style={styles.milestoneBar}>
        <Animated.View style={[styles.milestoneBarFill, animatedStyle]} />
      </View>
      <View style={styles.milestoneStats}>
        <Text style={styles.milestoneStatsText}>
          {completedMilestones.length} of {milestones.length} milestones completed
        </Text>
        <Text style={styles.milestonePercentage}>{Math.round(progressPercentage)}%</Text>
      </View>
    </View>
  );
};

// Productivity Analytics Component
interface ProductivityAnalyticsProps {
  tasks: Task[];
}

const ProductivityAnalytics: React.FC<ProductivityAnalyticsProps> = ({ tasks }) => {
  // Calculate productivity metrics
  const completedTasks = tasks.filter(task => task.status === 'completed');
  const totalTasks = tasks.length;
  const completionRate = totalTasks > 0 ? (completedTasks.length / totalTasks) * 100 : 0;

  // Calculate average completion time
  const tasksWithDuration = completedTasks.filter(task => task.actual_duration);
  const avgCompletionTime = tasksWithDuration.length > 0
    ? tasksWithDuration.reduce((sum, task) => sum + (task.actual_duration || 0), 0) / tasksWithDuration.length
    : 0;

  // Calculate productivity by priority
  const priorityStats = ['urgent', 'high', 'medium', 'low'].map(priority => {
    const priorityTasks = tasks.filter(task => task.priority === priority);
    const priorityCompleted = priorityTasks.filter(task => task.status === 'completed');
    const rate = priorityTasks.length > 0 ? (priorityCompleted.length / priorityTasks.length) * 100 : 0;

    return {
      priority,
      total: priorityTasks.length,
      completed: priorityCompleted.length,
      rate,
      color: priority === 'urgent' ? '#EF4444' :
             priority === 'high' ? '#F59E0B' :
             priority === 'medium' ? '#3B82F6' : '#10B981'
    };
  });

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${Math.round(minutes)}m`;
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}h ${mins}m`;
  };

  return (
    <View style={styles.productivityAnalytics}>
      <Text style={styles.analyticsTitle}>Productivity Analytics</Text>

      {/* Overall Stats */}
      <View style={styles.analyticsOverview}>
        <View style={styles.analyticsCard}>
          <Activity size={20} color="#3B82F6" />
          <Text style={styles.analyticsValue}>{Math.round(completionRate)}%</Text>
          <Text style={styles.analyticsLabel}>Completion Rate</Text>
        </View>
        <View style={styles.analyticsCard}>
          <Clock size={20} color="#10B981" />
          <Text style={styles.analyticsValue}>{formatDuration(avgCompletionTime)}</Text>
          <Text style={styles.analyticsLabel}>Avg. Time</Text>
        </View>
        <View style={styles.analyticsCard}>
          <TrendingUp size={20} color="#F59E0B" />
          <Text style={styles.analyticsValue}>{completedTasks.length}</Text>
          <Text style={styles.analyticsLabel}>Completed</Text>
        </View>
      </View>

      {/* Priority Breakdown */}
      <View style={styles.priorityBreakdown}>
        <Text style={styles.breakdownTitle}>Completion by Priority</Text>
        {priorityStats.map((stat) => (
          <View key={stat.priority} style={styles.priorityRow}>
            <View style={styles.priorityInfo}>
              <View style={[styles.priorityDot, { backgroundColor: stat.color }]} />
              <Text style={styles.priorityName}>
                {stat.priority.charAt(0).toUpperCase() + stat.priority.slice(1)}
              </Text>
            </View>
            <View style={styles.priorityStats}>
              <Text style={styles.priorityCount}>{stat.completed}/{stat.total}</Text>
              <Text style={[styles.priorityRate, { color: stat.color }]}>
                {Math.round(stat.rate)}%
              </Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

// Enhanced Task Card Component
interface TaskCardProps {
  task: Task;
  onToggleComplete: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onUpdateProgress: (progress: number) => void;
  getPriorityColor: (priority: Task['priority']) => string;
  getPriorityIcon: (priority: Task['priority']) => React.ReactNode;
  getStatusIcon: (status: Task['status']) => React.ReactNode;
  formatDate: (date: Date) => string;
  isOverdue: (date: Date) => boolean;
  getSubjectById: (id: string) => any;
}

const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onToggleComplete,
  onEdit,
  onUpdateProgress,
  getPriorityColor,
  getPriorityIcon,
  getStatusIcon,
  formatDate,
  isOverdue,
  getSubjectById,
}) => {
  const subject = task.subject_id ? getSubjectById(task.subject_id) : null;
  const isTaskOverdue = task.due_date && isOverdue(task.due_date);

  return (
    <View style={[
      styles.taskCard,
      isTaskOverdue && styles.overdueCard,
      task.status === 'completed' && styles.completedCard
    ]}>
      {/* Task Header */}
      <View style={styles.taskHeader}>
        <TouchableOpacity onPress={onToggleComplete} style={styles.statusButton}>
          {getStatusIcon(task.status)}
        </TouchableOpacity>

        <View style={styles.taskInfo}>
          <View style={styles.taskTitleRow}>
            <Text style={[
              styles.taskTitle,
              task.status === 'completed' && styles.completedTitle
            ]}>
              {task.title}
            </Text>
            {task.is_milestone && (
              <View style={styles.milestoneTag}>
                <Star size={12} color="#F59E0B" />
              </View>
            )}
          </View>

          {task.description && (
            <Text style={[
              styles.taskDescription,
              task.status === 'completed' && styles.completedDescription
            ]}>
              {task.description}
            </Text>
          )}

          {/* Tags */}
          {task.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {task.tags.slice(0, 3).map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                </View>
              ))}
              {task.tags.length > 3 && (
                <Text style={styles.moreTagsText}>+{task.tags.length - 3}</Text>
              )}
            </View>
          )}

          {/* Subject */}
          {subject && (
            <View style={styles.subjectTag}>
              <View style={[styles.subjectDot, { backgroundColor: subject.color }]} />
              <Text style={styles.subjectName}>{subject.name}</Text>
            </View>
          )}
        </View>

        <TouchableOpacity onPress={onEdit} style={styles.editButton}>
          <MoreVertical size={16} color="#6B7280" />
        </TouchableOpacity>
      </View>

      {/* Enhanced Progress Bar */}
      {task.progress_percentage > 0 && (
        <View style={styles.progressContainer}>
          <View style={styles.progressHeader}>
            <Text style={styles.progressLabel}>Progress</Text>
            <View style={styles.progressBadge}>
              <Text style={styles.progressText}>{task.progress_percentage}%</Text>
            </View>
          </View>
          <View style={styles.progressBar}>
            <Animated.View
              style={[
                styles.progressFill,
                {
                  width: `${task.progress_percentage}%`,
                  backgroundColor: getPriorityColor(task.priority)
                }
              ]}
            />
            {task.progress_percentage >= 100 && (
              <View style={styles.completionIndicator}>
                <CheckCircle size={12} color="#10B981" />
              </View>
            )}
          </View>
          {task.is_milestone && (
            <View style={styles.milestoneIndicator}>
              <Award size={14} color="#F59E0B" />
              <Text style={styles.milestoneText}>Milestone</Text>
            </View>
          )}

          {/* Interactive Progress Slider */}
          {task.status !== 'completed' && task.status !== 'cancelled' && (
            <View style={styles.progressSliderContainer}>
              <Text style={styles.sliderLabel}>Update Progress</Text>
              <View style={styles.progressSlider}>
                <TouchableOpacity
                  style={[styles.progressSliderTrack, { width: '100%' }]}
                  onPress={(event) => {
                    const { locationX } = event.nativeEvent;
                    const sliderWidth = 200; // Approximate slider width
                    const newProgress = Math.round((locationX / sliderWidth) * 100);
                    const clampedProgress = Math.max(0, Math.min(100, newProgress));
                    onUpdateProgress(clampedProgress);
                  }}
                >
                  <View style={[styles.progressSliderFill, { width: `${task.progress_percentage}%` }]} />
                  <View
                    style={[
                      styles.progressSliderThumb,
                      { left: `${task.progress_percentage}%` }
                    ]}
                  />
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      )}

      {/* Task Meta */}
      <View style={styles.taskMeta}>
        <View style={styles.taskMetaLeft}>
          {/* Priority */}
          <View style={styles.priorityTag}>
            {getPriorityIcon(task.priority)}
            <Text style={[styles.priorityText, { color: getPriorityColor(task.priority) }]}>
              {task.priority}
            </Text>
          </View>
        </View>

        <View style={styles.taskMetaRight}>
          {/* Due Date */}
          {task.due_date && (
            <View style={styles.dueDateContainer}>
              <CalendarIcon size={14} color={isTaskOverdue ? '#EF4444' : '#6B7280'} />
              <Text style={[
                styles.dueDateText,
                isTaskOverdue && styles.overdueDateText
              ]}>
                {formatDate(task.due_date)}
              </Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

export default function GoalsScreen() {
  const {
    tasks,
    loading,
    createTask,
    updateTask,
    deleteTask,
    completeTask,
    updateTaskProgress,
  } = useTasks();

  const { getSubjectById } = useSubjects();

  // Task reminders and notifications
  const {
    scheduleTaskReminder,
    cancelTaskReminders,
    showTaskCompletionNotification,
    showProductivityMilestone,
  } = useTaskReminders({
    tasks,
    onTaskCompleted: (task) => {
      console.log('Task completed:', task.title);
    },
    onStreakAchieved: (streakCount, streakType) => {
      console.log(`${streakType} streak achieved:`, streakCount);
    },
  });

  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);

  // Form states
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [dueDate, setDueDate] = useState<Date | undefined>(undefined);
  const [priority, setPriority] = useState<'low' | 'medium' | 'high' | 'urgent'>('medium');
  const [reminderEnabled, setReminderEnabled] = useState(false);
  const [reminderTime, setReminderTime] = useState<Date | undefined>(undefined);
  const [periodicReminders, setPeriodicReminders] = useState(false);
  const [reminderInterval, setReminderInterval] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  const [selectedSubject, setSelectedSubject] = useState<any>(null);
  const [tags, setTags] = useState<string[]>([]);
  const [isMilestone, setIsMilestone] = useState(false);

  // Date/Time picker states
  const [showDueDatePicker, setShowDueDatePicker] = useState(false);
  const [showReminderDatePicker, setShowReminderDatePicker] = useState(false);
  const [showReminderTimePicker, setShowReminderTimePicker] = useState(false);

  // Filter states
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilters, setActiveFilters] = useState<TaskFilters>({});
  const [selectedView, setSelectedView] = useState<'all' | 'todo' | 'in_progress' | 'completed'>('all');

  // UI states
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [showStats, setShowStats] = useState(false);

  const modalScale = useSharedValue(0);
  const filterModalScale = useSharedValue(0);

  // Helper function for periodic reminders
  const schedulePeriodicReminders = async (task: Task, interval: 'daily' | 'weekly' | 'monthly') => {
    try {
      // This would integrate with your reminder system to schedule recurring notifications
      console.log(`Scheduling ${interval} reminders for task: ${task.title}`);
      // Implementation would depend on your notification system
    } catch (error) {
      console.error('Error scheduling periodic reminders:', error);
    }
  };

  // Reset form function
  const resetForm = () => {
    setTitle('');
    setDescription('');
    setDueDate(undefined);
    setPriority('medium');
    setSelectedSubject(null);
    setTags([]);
    setIsMilestone(false);
    setReminderEnabled(false);
    setReminderTime(undefined);
    setPeriodicReminders(false);
    setReminderInterval('daily');
  };

  const openAddModal = () => {
    setEditingTask(null);
    resetForm();
    setShowAddModal(true);
    modalScale.value = withSpring(1);
  };

  const openEditModal = (task: Task) => {
    setEditingTask(task);
    setTitle(task.title);
    setDescription(task.description || '');
    setDueDate(task.due_date || undefined);
    setPriority(task.priority);
    setSelectedSubject(task.subject_id ? getSubjectById(task.subject_id) : null);
    setTags(task.tags);
    setIsMilestone(task.is_milestone);
    setReminderEnabled(task.reminder_enabled || false);
    setReminderTime(task.reminder_time || undefined);
    setPeriodicReminders(false); // Default for existing tasks
    setReminderInterval('daily');
    setShowAddModal(true);
    modalScale.value = withSpring(1);
  };

  const closeModal = () => {
    modalScale.value = withSpring(0, {}, () => {
      runOnJS(setShowAddModal)(false);
    });
  };

  const openFilterModal = () => {
    setShowFilterModal(true);
    filterModalScale.value = withSpring(1);
  };

  const closeFilterModal = () => {
    filterModalScale.value = withSpring(0, {}, () => {
      runOnJS(setShowFilterModal)(false);
    });
  };

  const handleSave = async () => {
    if (!title.trim()) {
      Alert.alert('Error', 'Please enter a task title');
      return;
    }

    const taskData = {
      title: title.trim(),
      description: description.trim(),
      priority,
      due_date: dueDate,
      subject_id: selectedSubject?.id,
      tags,
      is_milestone: isMilestone,
      reminder_enabled: reminderEnabled,
      reminder_time: reminderEnabled && reminderTime ? reminderTime : undefined,
    };

    try {
      let savedTask;
      if (editingTask) {
        savedTask = await updateTask(editingTask.id, taskData);
      } else {
        savedTask = await createTask(taskData);
      }

      // Schedule reminders if enabled
      if (savedTask && reminderEnabled) {
        if (reminderTime) {
          await scheduleTaskReminder(savedTask, 'custom');

          // Schedule periodic reminders if enabled
          if (periodicReminders) {
            await schedulePeriodicReminders(savedTask, reminderInterval);
          }
        }
        if (savedTask.due_date) {
          await scheduleTaskReminder(savedTask, 'due_soon');
        }
        if (savedTask.is_milestone) {
          await scheduleTaskReminder(savedTask, 'milestone_due');
        }
      }

      closeModal();
    } catch (error) {
      console.error('Error saving task:', error);
      Alert.alert('Error', 'Failed to save task. Please try again.');
    }
  };

  const handleDelete = (task: Task) => {
    Alert.alert(
      'Delete Task',
      `Are you sure you want to delete "${task.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteTask(task.id);
            } catch (error) {
              Alert.alert('Error', 'Failed to delete task. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleToggleComplete = async (task: Task) => {
    try {
      if (task.status === 'completed') {
        // Uncomplete task
        await updateTask(task.id, {
          status: 'todo',
          progress_percentage: 0,
          completion_date: undefined
        });

        // Cancel completion notifications and reschedule reminders
        await cancelTaskReminders(task.id);
        if (task.reminder_enabled) {
          await scheduleTaskReminder(task, 'custom');
        }
      } else {
        // Complete task
        await completeTask(task.id);

        // Show completion notification
        await showTaskCompletionNotification(task);

        // Cancel any pending reminders for this task
        await cancelTaskReminders(task.id);

        // Check for productivity milestones
        const completedTasks = tasks.filter(t => t.status === 'completed').length + 1;
        if (completedTasks % 10 === 0) {
          await showProductivityMilestone(`You've completed ${completedTasks} tasks! Amazing progress!`);
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to update task. Please try again.');
    }
  };

  // Utility functions
  const getPriorityColor = (priority: 'low' | 'medium' | 'high' | 'urgent') => {
    switch (priority) {
      case 'urgent': return '#DC2626';
      case 'high': return '#EF4444';
      case 'medium': return '#F59E0B';
      case 'low': return '#10B981';
      default: return '#6B7280';
    }
  };

  const getPriorityIcon = (priority: 'low' | 'medium' | 'high' | 'urgent') => {
    switch (priority) {
      case 'urgent': return <AlertCircle size={16} color="#DC2626" />;
      case 'high': return <Flag size={16} color="#EF4444" />;
      case 'medium': return <Clock size={16} color="#F59E0B" />;
      case 'low': return <Circle size={16} color="#10B981" />;
      default: return <Circle size={16} color="#6B7280" />;
    }
  };

  const getStatusColor = (status: Task['status']) => {
    switch (status) {
      case 'completed': return '#10B981';
      case 'in_progress': return '#3B82F6';
      case 'cancelled': return '#6B7280';
      default: return '#F59E0B';
    }
  };

  const getStatusIcon = (status: Task['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle size={20} color="#10B981" />;
      case 'in_progress': return <PlayCircle size={20} color="#3B82F6" />;
      case 'cancelled': return <X size={20} color="#6B7280" />;
      default: return <Circle size={20} color="#F59E0B" />;
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const isOverdue = (date: Date) => {
    return date < new Date() && date.toDateString() !== new Date().toDateString();
  };



  // Filter and sort tasks
  const getFilteredTasks = () => {
    let filtered = tasks;

    // Apply view filter
    if (selectedView !== 'all') {
      filtered = filtered.filter(task => task.status === selectedView);
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(query) ||
        task.description?.toLowerCase().includes(query) ||
        task.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    return filtered.sort((a, b) => {
      // Sort by priority first (urgent > high > medium > low)
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;

      // Then by due date (closest first)
      if (a.due_date && b.due_date) {
        return a.due_date.getTime() - b.due_date.getTime();
      }
      if (a.due_date) return -1;
      if (b.due_date) return 1;

      // Finally by creation date (newest first)
      return b.created_at.getTime() - a.created_at.getTime();
    });
  };

  const modalAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: modalScale.value }],
  }));

  const filterModalAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: filterModalScale.value }],
  }));

  // Get filtered and organized data
  const filteredTasks = getFilteredTasks();
  const overdueTasks = tasks.filter(task => task.due_date && isOverdue(task.due_date));
  const upcomingTasks = tasks.filter(task =>
    task.due_date &&
    !isOverdue(task.due_date) &&
    task.due_date <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
  );
  const todoTasks = tasks.filter(task => task.status === 'todo');
  const inProgressTasks = tasks.filter(task => task.status === 'in_progress');
  const completedTasks = tasks.filter(task => task.status === 'completed');

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#F8FAFC', '#EEF2FF', '#F8FAFC']}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <IsotopeLogo size="medium" />
            <Text style={styles.subtitle}>Task Management</Text>
          </View>
          <View style={styles.headerRight}>
            <TouchableOpacity style={styles.statsButton} onPress={() => setShowStats(!showStats)}>
              <BarChart3 size={20} color="#6366F1" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.filterButton} onPress={openFilterModal}>
              <Filter size={20} color="#6366F1" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.addButton} onPress={openAddModal}>
              <Plus size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Search size={20} color="#6B7280" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search tasks..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#9CA3AF"
            />
          </View>
        </View>

        {/* Quick Stats */}
        {showStats && (
          <Animated.View style={styles.statsContainer}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{todoTasks.length}</Text>
              <Text style={styles.statLabel}>To Do</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{inProgressTasks.length}</Text>
              <Text style={styles.statLabel}>In Progress</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{completedTasks.length}</Text>
              <Text style={styles.statLabel}>Completed</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={[styles.statNumber, { color: '#EF4444' }]}>{overdueTasks.length}</Text>
              <Text style={styles.statLabel}>Overdue</Text>
            </View>
          </Animated.View>
        )}
      </LinearGradient>

      {/* Enhanced Progress Visualization */}
      {showStats && (
        <ScrollView showsVerticalScrollIndicator={false}>
          <ProgressChart tasks={tasks} />
          <MilestoneProgress tasks={tasks} />
          <ProductivityAnalytics tasks={tasks} />
        </ScrollView>
      )}

      {/* Filter Tabs */}
      <View style={styles.filterTabs}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tabsContainer}>
          {['all', 'todo', 'in_progress', 'completed'].map((view) => (
            <TouchableOpacity
              key={view}
              style={[
                styles.filterTab,
                selectedView === view && styles.activeFilterTab
              ]}
              onPress={() => setSelectedView(view as any)}
            >
              <Text style={[
                styles.filterTabText,
                selectedView === view && styles.activeFilterTabText
              ]}>
                {view === 'all' ? 'All' :
                 view === 'todo' ? 'To Do' :
                 view === 'in_progress' ? 'In Progress' : 'Completed'}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>



      {/* Task List */}
      <ScrollView style={styles.taskListContainer} showsVerticalScrollIndicator={false}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading tasks...</Text>
          </View>
        ) : filteredTasks.length === 0 ? (
          <View style={styles.emptyState}>
            <Target size={48} color="#D1D5DB" />
            <Text style={styles.emptyTitle}>No tasks found</Text>
            <Text style={styles.emptyText}>
              {searchQuery ? 'Try adjusting your search or filters' : 'Create your first task to get started!'}
            </Text>
          </View>
        ) : (
          <FlatList
            data={filteredTasks}
            keyExtractor={(item) => item.id}
            renderItem={({ item: task }) => (
              <TaskCard
                task={task}
                onToggleComplete={() => handleToggleComplete(task)}
                onEdit={() => openEditModal(task)}
                onDelete={() => handleDelete(task)}
                onUpdateProgress={(progress) => updateTaskProgress(task.id, progress)}
                getPriorityColor={getPriorityColor}
                getPriorityIcon={getPriorityIcon}
                getStatusIcon={getStatusIcon}
                formatDate={formatDate}
                isOverdue={isOverdue}
                getSubjectById={getSubjectById}
              />
            )}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        )}
      </ScrollView>

      {/* Add/Edit Task Modal */}
      <Modal visible={showAddModal} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <Animated.View style={[styles.modalContent, modalAnimatedStyle]}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {editingTask ? 'Edit Task' : 'Create New Task'}
              </Text>
              <TouchableOpacity onPress={closeModal}>
                <X size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
              {/* Task Title */}
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Task Title *</Text>
                <TextInput
                  style={styles.textInput}
                  value={title}
                  onChangeText={setTitle}
                  placeholder="Enter task title"
                  placeholderTextColor="#9CA3AF"
                />
              </View>

              {/* Description */}
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Description</Text>
                <TextInput
                  style={[styles.textInput, styles.textArea]}
                  value={description}
                  onChangeText={setDescription}
                  placeholder="Enter task description"
                  placeholderTextColor="#9CA3AF"
                  multiline
                  numberOfLines={3}
                />
              </View>

              {/* Due Date */}
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Due Date</Text>
                <TouchableOpacity
                  style={styles.datePickerButton}
                  onPress={() => setShowDueDatePicker(true)}
                >
                  <CalendarIcon size={20} color="#6B7280" />
                  <Text style={styles.datePickerText}>
                    {dueDate ? dueDate.toLocaleDateString() : 'Select due date'}
                  </Text>
                </TouchableOpacity>
                {showDueDatePicker && (
                  <DateTimePicker
                    value={dueDate || new Date()}
                    mode="date"
                    display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                    onChange={(event, selectedDate) => {
                      setShowDueDatePicker(false);
                      if (selectedDate) {
                        setDueDate(selectedDate);
                      }
                    }}
                  />
                )}
              </View>

              {/* Priority */}
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Priority</Text>
                <View style={styles.prioritySelector}>
                  {(['low', 'medium', 'high', 'urgent'] as const).map((p) => (
                    <TouchableOpacity
                      key={p}
                      style={[
                        styles.priorityButton,
                        priority === p && styles.priorityButtonActive,
                        { borderColor: getPriorityColor(p) },
                      ]}
                      onPress={() => setPriority(p)}
                    >
                      {getPriorityIcon(p)}
                      <Text
                        style={[
                          styles.priorityButtonText,
                          priority === p && { color: getPriorityColor(p) },
                        ]}
                      >
                        {p.charAt(0).toUpperCase() + p.slice(1)}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Milestone Toggle */}
              <View style={styles.inputGroup}>
                <TouchableOpacity
                  style={styles.checkboxRow}
                  onPress={() => setIsMilestone(!isMilestone)}
                >
                  <View style={[styles.checkbox, isMilestone && styles.checkboxActive]}>
                    {isMilestone && <CheckCircle size={16} color="#FFFFFF" />}
                  </View>
                  <Text style={styles.checkboxLabel}>Mark as milestone</Text>
                  <Star size={16} color="#F59E0B" />
                </TouchableOpacity>
              </View>

              {/* Reminder Settings */}
              <View style={styles.inputGroup}>
                <TouchableOpacity
                  style={styles.checkboxRow}
                  onPress={() => setReminderEnabled(!reminderEnabled)}
                >
                  <View style={[styles.checkbox, reminderEnabled && styles.checkboxActive]}>
                    {reminderEnabled && <CheckCircle size={16} color="#FFFFFF" />}
                  </View>
                  <Text style={styles.checkboxLabel}>Enable reminders</Text>
                  <Bell size={16} color="#3B82F6" />
                </TouchableOpacity>

                {reminderEnabled && (
                  <View style={styles.reminderTimeContainer}>
                    <Text style={styles.reminderTimeLabel}>Reminder Date & Time</Text>

                    {/* Reminder Date */}
                    <TouchableOpacity
                      style={styles.textInput}
                      onPress={() => setShowReminderDatePicker(true)}
                    >
                      <View style={styles.dateTimePickerRow}>
                        <CalendarIcon size={16} color="#6B7280" />
                        <Text style={styles.dateTimePickerText}>
                          {reminderTime ? reminderTime.toLocaleDateString() : 'Select date'}
                        </Text>
                      </View>
                    </TouchableOpacity>

                    {/* Reminder Time */}
                    <TouchableOpacity
                      style={styles.textInput}
                      onPress={() => setShowReminderTimePicker(true)}
                    >
                      <View style={styles.dateTimePickerRow}>
                        <Clock size={16} color="#6B7280" />
                        <Text style={styles.dateTimePickerText}>
                          {reminderTime ? reminderTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : 'Select time'}
                        </Text>
                      </View>
                    </TouchableOpacity>

                    {/* Periodic Reminders */}
                    <TouchableOpacity
                      style={styles.checkboxRow}
                      onPress={() => setPeriodicReminders(!periodicReminders)}
                    >
                      <View style={[styles.checkbox, periodicReminders && styles.checkboxActive]}>
                        {periodicReminders && <CheckCircle size={16} color="#FFFFFF" />}
                      </View>
                      <Text style={styles.checkboxLabel}>Repeat reminders</Text>
                    </TouchableOpacity>

                    {periodicReminders && (
                      <View style={styles.reminderIntervalContainer}>
                        <Text style={styles.reminderIntervalLabel}>Repeat every:</Text>
                        <View style={styles.intervalSelector}>
                          {(['daily', 'weekly', 'monthly'] as const).map((interval) => (
                            <TouchableOpacity
                              key={interval}
                              style={[
                                styles.intervalButton,
                                reminderInterval === interval && styles.intervalButtonActive
                              ]}
                              onPress={() => setReminderInterval(interval)}
                            >
                              <Text style={[
                                styles.intervalButtonText,
                                reminderInterval === interval && styles.intervalButtonActiveText
                              ]}>
                                {interval.charAt(0).toUpperCase() + interval.slice(1)}
                              </Text>
                            </TouchableOpacity>
                          ))}
                        </View>
                      </View>
                    )}

                    {/* Date/Time Pickers */}
                    {showReminderDatePicker && (
                      <DateTimePicker
                        value={reminderTime || new Date()}
                        mode="date"
                        display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                        onChange={(_, selectedDate) => {
                          setShowReminderDatePicker(false);
                          if (selectedDate) {
                            const newDateTime = reminderTime ? new Date(reminderTime) : new Date();
                            newDateTime.setFullYear(selectedDate.getFullYear());
                            newDateTime.setMonth(selectedDate.getMonth());
                            newDateTime.setDate(selectedDate.getDate());
                            setReminderTime(newDateTime);
                          }
                        }}
                      />
                    )}

                    {showReminderTimePicker && (
                      <DateTimePicker
                        value={reminderTime || new Date()}
                        mode="time"
                        display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                        onChange={(_, selectedTime) => {
                          setShowReminderTimePicker(false);
                          if (selectedTime) {
                            const newDateTime = reminderTime ? new Date(reminderTime) : new Date();
                            newDateTime.setHours(selectedTime.getHours());
                            newDateTime.setMinutes(selectedTime.getMinutes());
                            setReminderTime(newDateTime);
                          }
                        }}
                      />
                    )}

                    <Text style={styles.reminderHint}>
                      Set when you want to be reminded about this task
                    </Text>
                  </View>
                )}
              </View>

              {/* Subject */}
              <View style={styles.inputGroup}>
                <SubjectPicker
                  selectedSubject={selectedSubject}
                  onSelectSubject={setSelectedSubject}
                />
              </View>
            </ScrollView>
            
            <View style={styles.modalActions}>
              {editingTask && (
                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={() => {
                    closeModal();
                    if (editingTask) handleDelete(editingTask);
                  }}
                >
                  <Trash2 size={16} color="#EF4444" />
                  <Text style={styles.deleteText}>Delete</Text>
                </TouchableOpacity>
              )}

              <View style={styles.actionButtons}>
                <TouchableOpacity style={styles.cancelButton} onPress={closeModal}>
                  <Text style={styles.cancelText}>Cancel</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                  <LinearGradient
                    colors={['#6366F1', '#8B5CF6']}
                    style={styles.saveGradient}
                  >
                    <Text style={styles.saveText}>
                      {editingTask ? 'Update' : 'Create'}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </Animated.View>
        </View>
      </Modal>

      {/* Filter Modal */}
      <Modal visible={showFilterModal} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <Animated.View style={[styles.filterModalContent, filterModalAnimatedStyle]}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filter Tasks</Text>
              <TouchableOpacity onPress={closeFilterModal}>
                <X size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {/* Status Filter */}
              <View style={styles.filterSection}>
                <Text style={styles.filterSectionTitle}>Status</Text>
                <View style={styles.filterOptions}>
                  {['all', 'todo', 'in_progress', 'completed'].map((status) => (
                    <TouchableOpacity
                      key={status}
                      style={[
                        styles.filterOption,
                        selectedView === status && styles.filterOptionActive
                      ]}
                      onPress={() => setSelectedView(status as any)}
                    >
                      <Text style={[
                        styles.filterOptionText,
                        selectedView === status && styles.filterOptionActiveText
                      ]}>
                        {status === 'all' ? 'All' :
                         status === 'todo' ? 'To Do' :
                         status === 'in_progress' ? 'In Progress' : 'Completed'}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>


            </ScrollView>

            <View style={styles.modalActions}>
              <TouchableOpacity style={styles.cancelButton} onPress={closeFilterModal}>
                <Text style={styles.cancelText}>Close</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statsButton: {
    backgroundColor: '#FFFFFF',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  filterButton: {
    backgroundColor: '#FFFFFF',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  addButton: {
    width: 44,
    height: 44,
    backgroundColor: '#6366F1',
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#1F2937',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingTop: 16,
    gap: 12,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  filterTabs: {
    paddingTop: 16,
  },
  tabsContainer: {
    paddingHorizontal: 20,
  },
  filterTab: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  activeFilterTab: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  filterTabText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  activeFilterTabText: {
    color: '#FFFFFF',
  },
  categoryFilter: {
    paddingTop: 12,
    paddingHorizontal: 20,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  activeCategoryChip: {
    backgroundColor: '#EEF2FF',
    borderColor: '#6366F1',
  },
  categoryChipText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  activeCategoryChipText: {
    color: '#6366F1',
  },
  categoryDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  taskListContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  // Task Card Styles
  taskCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  overdueCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#EF4444',
  },
  completedCard: {
    opacity: 0.7,
  },
  taskHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  statusButton: {
    marginRight: 12,
    marginTop: 2,
  },
  taskInfo: {
    flex: 1,
  },
  taskTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    flex: 1,
  },
  completedTitle: {
    textDecorationLine: 'line-through',
    color: '#6B7280',
  },
  milestoneTag: {
    marginLeft: 8,
  },
  taskDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
    lineHeight: 20,
  },
  completedDescription: {
    color: '#9CA3AF',
  },
  tagsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tag: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 6,
  },
  tagText: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  moreTagsText: {
    fontSize: 12,
    color: '#9CA3AF',
    fontStyle: 'italic',
  },
  subjectTag: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  subjectDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  subjectName: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  editButton: {
    padding: 4,
  },
  progressContainer: {
    marginBottom: 12,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  progressBadge: {
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  progressBar: {
    height: 6,
    backgroundColor: '#F3F4F6',
    borderRadius: 3,
    position: 'relative',
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 10,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  completionIndicator: {
    position: 'absolute',
    right: 2,
    top: -3,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 1,
  },
  milestoneIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginTop: 6,
  },
  milestoneText: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#F59E0B',
  },
  // Progress Slider Styles
  progressSliderContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  sliderLabel: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginBottom: 8,
  },
  progressSlider: {
    paddingVertical: 8,
  },
  progressSliderTrack: {
    height: 20,
    backgroundColor: '#F3F4F6',
    borderRadius: 10,
    position: 'relative',
    justifyContent: 'center',
  },
  progressSliderFill: {
    height: 6,
    backgroundColor: '#3B82F6',
    borderRadius: 3,
    position: 'absolute',
    top: 7,
    left: 0,
  },
  progressSliderThumb: {
    width: 16,
    height: 16,
    backgroundColor: '#3B82F6',
    borderRadius: 8,
    position: 'absolute',
    top: 2,
    marginLeft: -8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },
  taskMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  taskMetaLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  taskMetaRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  categoryTag: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryText: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  priorityTag: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '500',
  },
  dueDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  dueDateText: {
    fontSize: 12,
    color: '#6B7280',
  },
  overdueDateText: {
    color: '#EF4444',
    fontWeight: '500',
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  durationText: {
    fontSize: 12,
    color: '#6B7280',
  },
  statsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginVertical: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  section: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  goalMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  goalDate: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  goalDateText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  goalPriority: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },

  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    width: '100%',
    maxWidth: 400,
    maxHeight: '90%',
  },
  filterModalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  categorySelector: {
    paddingVertical: 8,
  },
  categoryOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 12,
    borderRadius: 12,
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  categoryOptionActive: {
    backgroundColor: '#EEF2FF',
    borderColor: '#6366F1',
  },
  categoryOptionText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  categoryOptionActiveText: {
    color: '#6366F1',
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxActive: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  checkboxLabel: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
  },
  filterSection: {
    marginBottom: 24,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  filterOptions: {
    gap: 8,
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  filterOptionActive: {
    backgroundColor: '#EEF2FF',
    borderColor: '#6366F1',
  },
  filterOptionText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  filterOptionActiveText: {
    color: '#6366F1',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  modalBody: {
    padding: 20,
    maxHeight: 400,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#1F2937',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  prioritySelector: {
    flexDirection: 'row',
    gap: 8,
  },
  priorityButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    gap: 6,
  },
  priorityButtonActive: {
    backgroundColor: '#F3F4F6',
  },
  priorityButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  modalActions: {
    padding: 20,
    gap: 16,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 12,
    backgroundColor: '#FEF2F2',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  deleteText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#EF4444',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 14,
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  saveButton: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  saveGradient: {
    paddingVertical: 14,
    alignItems: 'center',
  },
  saveText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
  // Progress Chart Styles
  progressChart: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  chartContainer: {
    gap: 12,
  },
  chartBar: {
    gap: 8,
  },
  barContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  barBackground: {
    height: 8,
    backgroundColor: '#F3F4F6',
    borderRadius: 4,
    overflow: 'hidden',
    flex: 1,
  },
  barFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressPercentage: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    minWidth: 35,
    textAlign: 'right',
  },
  barInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryName: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  taskCount: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  // Milestone Progress Styles
  milestoneProgress: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  milestoneHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  milestoneTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  milestoneBar: {
    height: 12,
    backgroundColor: '#FEF3C7',
    borderRadius: 6,
    overflow: 'hidden',
    marginBottom: 12,
  },
  milestoneBarFill: {
    height: '100%',
    backgroundColor: '#F59E0B',
    borderRadius: 6,
  },
  milestoneStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  milestoneStatsText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  milestonePercentage: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#F59E0B',
  },
  // Productivity Analytics Styles
  productivityAnalytics: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  analyticsTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  analyticsOverview: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  analyticsCard: {
    flex: 1,
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    marginHorizontal: 4,
  },
  analyticsValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginTop: 8,
    marginBottom: 4,
  },
  analyticsLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  priorityBreakdown: {
    gap: 12,
  },
  breakdownTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 12,
  },
  priorityRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  priorityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  priorityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  priorityName: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  priorityStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  priorityCount: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  priorityRate: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    minWidth: 40,
    textAlign: 'right',
  },
  // Reminder Settings Styles
  reminderTimeContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  reminderTimeLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: 8,
  },
  reminderHint: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
    fontStyle: 'italic',
  },
  // Date/Time Picker Styles
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  datePickerText: {
    fontSize: 16,
    color: '#1F2937',
    flex: 1,
  },
  dateTimePickerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  dateTimePickerText: {
    fontSize: 16,
    color: '#1F2937',
    flex: 1,
  },
  reminderIntervalContainer: {
    marginTop: 12,
  },
  reminderIntervalLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: 8,
  },
  intervalSelector: {
    flexDirection: 'row',
    gap: 8,
  },
  intervalButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
  },
  intervalButtonActive: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  intervalButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  intervalButtonActiveText: {
    color: '#FFFFFF',
  },
});